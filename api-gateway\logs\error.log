{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:08:57","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:08:57","url":"/unknown-route"}
{"contentLength":"599","duration":"54ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:10:24","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:10:24","url":"/unknown-route"}
{"contentLength":"599","duration":"73ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:15:47","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:15:47","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:20:51","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:20:51","url":"/unknown-route"}
{"contentLength":"599","duration":"82ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:22:04","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:22:04","url":"/unknown-route"}
{"contentLength":"599","duration":"59ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 14:25:52","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 14:25:52","url":"/unknown-route"}
{"contentLength":"85","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 17:31:16","url":"/auth/"}
{"contentLength":"604","duration":"52ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:31:26","url":"/health"}
{"contentLength":"606","duration":"136ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:32:46","url":"/health"}
{"contentLength":"605","duration":"35ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:26","url":"/health"}
{"contentLength":"1470","duration":"131ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"238","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 17:33:27","url":"/auth/register"}
{"contentLength":"606","duration":"254ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:28","url":"/health"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 03:57:29","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-16 03:57:29"}
{"contentLength":"114","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 03:57:29","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"eaa2cb86-2f6e-453a-82ba-385ef0a906a0"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:05"}
{"contentLength":"118","duration":"3955ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:05","url":"/auth/register"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND auth-service","level":"error","message":"Auth service proxy error","service":"api-gateway","timestamp":"2025-07-15 21:51:09"}
{"contentLength":"118","duration":"3940ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 21:51:09","url":"/auth/login"}
{"contentLength":"650","duration":"53ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 22:31:59","url":"/health"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 22:32:32","url":"/auth/health"}
{"contentLength":"86","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 22:32:32","url":"/archive/health"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 22:32:33","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"108","duration":"42ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-15 22:32:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:19","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:19","url":"/archive/health"}
{"contentLength":"375","duration":"23ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:19","url":"/auth/register"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:08:41","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/assessments/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:08:41","url":"/archive/health"}
{"contentLength":"375","duration":"15ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:08:41","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:08:41","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:08:43"}
{"contentLength":"114","duration":"2547ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:08:43","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:10:34","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:10:34","url":"/archive/health"}
{"contentLength":"375","duration":"21ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:10:34","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:10:34","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:10:37"}
{"contentLength":"114","duration":"2543ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:10:37","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:11:17","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:11:17","url":"/archive/health"}
{"contentLength":"375","duration":"20ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:11:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:11:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"code":"ENOTFOUND","error":"getaddrinfo ENOTFOUND assessment-service","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-15 23:11:20"}
{"contentLength":"114","duration":"2535ms","error":"Server Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-15 23:11:20","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:02","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/archive/health"}
{"contentLength":"375","duration":"17ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:02","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:02","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"49ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:02","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:13:51","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/archive/health"}
{"contentLength":"375","duration":"16ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:13:51","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:13:51","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"52ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:13:51","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:14:45","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/archive/health"}
{"contentLength":"375","duration":"51ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:14:45","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:14:45","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"99","duration":"57ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:14:45","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:16:17","url":"/auth/health"}
{"contentLength":"86","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:16:17","url":"/archive/health"}
{"contentLength":"375","duration":"47ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:16:17","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:16:17","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:20:58","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:20:58","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:20:58","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:20:59","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"0ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:27:52","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:27:52","url":"/archive/health"}
{"contentLength":"375","duration":"31ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:27:52","url":"/auth/register"}
{"error":"Unexpected token o in JSON at position 1","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-15 23:27:53","userId":"ee31036f-f7f7-4b34-be4e-35ecdab587da"}
{"contentLength":"91","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:32:48","url":"/auth/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:48","url":"/archive/health"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:32:49","url":"/assessments/submit"}
{"contentLength":"91","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-15 23:34:09","url":"/auth/health"}
{"contentLength":"86","duration":"2ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/health"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/archive/health"}
{"contentLength":"375","duration":"28ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-15 23:34:09","url":"/auth/register"}
{"contentLength":"82","duration":"4ms","error":"Client Error","ip":"::ffff:**********","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-15 23:34:09","url":"/assessments/submit"}
{"contentLength":"599","duration":"79ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 08:16:14","url":"/health"}
{"contentLength":"93","duration":"0ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-16 08:16:14","url":"/unknown-route"}
{"contentLength":"599","duration":"72ms","error":"Server Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 09:51:46","url":"/health"}
{"contentLength":"93","duration":"1ms","error":"Client Error","ip":"::ffff:127.0.0.1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":404,"timestamp":"2025-07-16 09:51:46","url":"/unknown-route"}
{"contentLength":"120","duration":"32ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:26:33","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"10ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:26:33","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"12ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:28:54","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 13:28:54","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"13ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:46","url":"/archive/results?page=1&limit=10","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"11ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:46","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:46"}
{"contentLength":"111","duration":"7ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:47","url":"/archive/results?page=1&limit=10","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Archive service proxy error","service":"api-gateway","timestamp":"2025-07-16 13:36:47"}
{"contentLength":"111","duration":"5ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-16 13:36:47","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 13:55:54","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"2006","duration":"428ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 13:55:55","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"44ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:42:04","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:42:04","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:00","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"8ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:00","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:11","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"19ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:43:11","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"27ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:44","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:44","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"8ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:57","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"120","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":403,"timestamp":"2025-07-16 14:46:57","url":"/archive/stats/summary","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 15:35:15","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"2006","duration":"232ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 15:35:15","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"602b0e9c-3a4c-40a3-bf3b-e2f0e9f08309"}
{"contentLength":"548","duration":"58ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 15:58:15","url":"/auth/register"}
{"contentLength":"564","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:17","url":"/auth/login"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:33","url":"/auth/profile"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"PUT","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:36","url":"/auth/profile"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:39","url":"/auth/token-balance"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:42","url":"/assessments/submit"}
{"contentLength":"86","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:45","url":"/archive/stats"}
{"contentLength":"564","duration":"36ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:50","url":"/auth/login"}
{"contentLength":"564","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:15:53","url":"/auth/login"}
{"contentLength":"564","duration":"53ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:32","url":"/auth/login"}
{"contentLength":"564","duration":"5ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:36","url":"/auth/login"}
{"contentLength":"564","duration":"40ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:18:49","url":"/auth/login"}
{"contentLength":"564","duration":"9ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-16 16:24:43","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:25:26","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"1672","duration":"15ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 16:25:26","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:28:13","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"1672","duration":"14ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-16 16:28:13","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:30:44","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 16:33:55","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-16 18:58:48","userId":"98116128-710f-4360-8dfa-4ce497f7e98f"}
{"contentLength":"564","duration":"11ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-17 06:00:23","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 08:01:55","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 10:47:33","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"contentLength":"246","duration":"45ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-17 10:47:33","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 10:49:39","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"contentLength":"246","duration":"20ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-17 10:49:39","url":"/assessments/submit","userEmail":"<EMAIL>","userId":"4c173080-0fbd-4938-8bba-5bb7217b0a63"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:13:16","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:22:48","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"code":"ECONNRESET","error":"socket hang up","level":"error","message":"Assessment service proxy error","service":"api-gateway","timestamp":"2025-07-17 12:28:00"}
{"contentLength":"114","duration":"60ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-17 12:28:00","url":"/assessments/status/cf24392d-c8e0-466a-98c2-7c0459953605","userEmail":"<EMAIL>","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 12:31:22","userId":"332672ba-aa6e-43b4-80d2-5c5c95122e65"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/auth/profile"}
{"contentLength":"128","duration":"7ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:55","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/stats"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:31:58","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:15","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:17","url":"/auth/logout"}
{"contentLength":"128","duration":"4ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:18","url":"/auth/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 12:32:23","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 18:23:11","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 18:26:39","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 19:31:29","userId":"a8be340d-ff32-4113-999e-60f05d47c6b3"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 19:34:32","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:27:31","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:28:33","userId":"4bca5142-e6b6-4b37-8d65-3020736087a8"}
{"contentLength":"128","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:03","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:04","url":"/archive/results/1566a067-09e2-4973-a382-c8dc4a973299"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:04","url":"/archive/results/1566a067-09e2-4973-a382-c8dc4a973299"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:05","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:06","url":"/auth/token-balance"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:06","url":"/auth/token-balance"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:07","url":"/auth/profile"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:09","url":"/auth/token-balance"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:09","url":"/auth/token-balance"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 20:30:10","url":"/auth/profile"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:33:46","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-17 20:36:28","url":"/assessments/status/830de7aa-050e-4ff5-9595-04939f6fbc83"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:38:31","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:46:59","userId":"4edcdf45-2eb3-49a8-9f88-69316c852cfc"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 20:58:39","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 21:06:48","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-17 21:07:18","userId":"7c99b87e-4b53-4040-8b29-980ba4e1321e"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/auth/profile"}
{"contentLength":"128","duration":"0ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:56","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:07:57","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:02","url":"/auth/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:03","url":"/auth/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-17 21:08:18","url":"/auth/login"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:53:50","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:55:38","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 03:56:19","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:30:23"}
{"contentLength":"124","duration":"26ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:30:23","url":"/admin/login"}
{"code":"ECONNREFUSED","error":"","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:30:44"}
{"contentLength":"124","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:30:44","url":"/admin/login"}
{"contentLength":"1867","duration":"50ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:35:39","url":"/admin/login"}
{"contentLength":"1867","duration":"31ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:36:04","url":"/admin/login"}
{"contentLength":"505","duration":"343ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:39:59","url":"/admin/login"}
{"code":"ERR_HTTP_HEADERS_SENT","error":"Cannot set headers after they are sent to the client","level":"error","message":"Admin auth service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:47:11"}
{"contentLength":"124","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:47:11","url":"/admin/login"}
{"contentLength":"505","duration":"324ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:49:10","url":"/admin/login"}
{"contentLength":"505","duration":"303ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":500,"timestamp":"2025-07-18 05:53:45","url":"/admin/login"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:13"}
{"contentLength":"117","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:13","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:13"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:13","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:28"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:28","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:28"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:28","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:32"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:32","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:54:32"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:54:32","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:41"}
{"contentLength":"117","duration":"3ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:41","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:41"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:41","url":"/admin/users?page=1&limit=5&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:43"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:43","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:43"}
{"contentLength":"117","duration":"4ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:43","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:45"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:45","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:45"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:45","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:49"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:49","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:55:49"}
{"contentLength":"117","duration":"2ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:55:49","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:58:52"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:58:52","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"code":"ERR_HTTP_INVALID_HEADER_VALUE","error":"Invalid value \"undefined\" for header \"X-Admin-ID\"","level":"error","message":"Admin archive service proxy error","service":"api-gateway","timestamp":"2025-07-18 05:59:27"}
{"contentLength":"117","duration":"1ms","error":"Server Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":503,"timestamp":"2025-07-18 05:59:27","url":"/admin/users?page=1&limit=10&search=&sortBy=created_at&sortOrder=DESC"}
{"contentLength":"82","duration":"6ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/profile"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/profile"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:43","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/profile"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/profile"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"82","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:17:47","url":"/admin/logout"}
{"contentLength":"83","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:18:04","url":"/admin/profile"}
{"contentLength":"83","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 06:18:04","url":"/admin/profile"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/profile"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:32:00","url":"/admin/logout"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:34:17","url":"/admin/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:34:20","url":"/admin/login"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 06:36:42","url":"/admin/login"}
{"contentLength":"82","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 07:03:43","url":"/admin/users?page=1&limit=5"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:20:31","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:29:40","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:34:16","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 07:40:10","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:21:48","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:22:12","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:23:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:24:00","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:32:37","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:38:34","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:52:55","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:55:30","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"error":"\"[object Object]\" is not valid JSON","level":"error","message":"Error processing assessment request body","service":"api-gateway","timestamp":"2025-07-18 08:58:33","userId":"b9016e17-3f5c-4849-874d-e5b6e9459f2b"}
{"contentLength":"128","duration":"3ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:14","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/stats"}
{"contentLength":"128","duration":"2ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/stats"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/auth/profile"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/archive/results?page=1&limit=10"}
{"contentLength":"128","duration":"1ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"GET","service":"api-gateway","statusCode":429,"timestamp":"2025-07-18 09:00:39","url":"/auth/profile"}
{"contentLength":"480","duration":"374ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 09:10:06","url":"/auth/login"}
{"contentLength":"548","duration":"49ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":400,"timestamp":"2025-07-18 09:10:31","url":"/auth/register"}
{"contentLength":"480","duration":"280ms","error":"Client Error","ip":"::1","level":"error","message":"Request completed with error","method":"POST","service":"api-gateway","statusCode":401,"timestamp":"2025-07-18 09:10:31","url":"/auth/login"}
