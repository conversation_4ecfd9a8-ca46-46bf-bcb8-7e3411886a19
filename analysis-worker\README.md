# Analysis Worker - ATMA Backend

Stateless worker service yang mengonsumsi job dari Message Queue, melakukan analisis menggunakan AI (Google Generative AI), dan menyimpan hasil ke Archive Service dalam ekosistem ATMA (AI-Driven Talent Mapping Assessment).

## 📋 Project Overview

ATMA adalah platform penilaian dan pemetaan bakat berbasis AI yang menggunakan arsitektur microservice. Analysis Worker bertanggung jawab untuk:
- Mengonsumsi job assessment dari RabbitMQ
- Melakukan analisis persona menggunakan Google Generative AI
- Menyimpan hasil analisis ke Archive Service dan mengirim notifikasi

## 🚀 Quick Start

### Prerequisites
- Node.js 18+
- RabbitMQ server running
- Google AI API Key (dari Google AI Studio)
- Archive Service dan Notification Service running
- Environment variables dikonfigurasi

### Installation & Setup

#### Option 1: Quick Start with Mock AI (Recommended for Testing)
```bash
# 1. Install dependencies
npm install

# 2. Start worker with mock AI model (no Gemini API key needed)
npm run dev:mock
```

#### Option 2: Setup with Real Gemini API
```bash
# 1. Install dependencies
npm install

# 2. Copy environment file
cp .env.example .env

# 3. Configure all required services in .env
# Edit GOOGLE_AI_API_KEY, RABBITMQ_URL, ARCHIVE_SERVICE_URL, NOTIFICATION_SERVICE_URL

# 4. Start worker
npm run dev
```

### Verify Installation
```bash
# For Mock AI Mode (recommended for testing)
npm run dev:mock
# Check logs - should show "Using mock AI model"

# For Real API Mode
npm run dev
# Check logs for successful startup

# Submit test job via Assessment Service to verify processing
# Worker akan memproses job dan menyimpan hasil ke Archive Service
```

## Fungsi Utama

1. **Queue Consumer**: Mengonsumsi job dari RabbitMQ
2. **Minimal Validation**: Validasi struktur message saja (trust assessment data dari Assessment Service)
3. **AI Analysis**: Menggunakan Google Generative AI untuk analisis persona
4. **Prompt Engineering**: Menyusun prompt kompleks berdasarkan data assessment
5. **Result Processing**: Memproses dan memvalidasi response dari AI
6. **Data Storage**: Menyimpan hasil ke Archive Service
7. **Error Handling**: Menangani error dan retry mechanism

## Validation Strategy

**Analysis Worker** menggunakan **optimized validation approach**:
- ✅ **Minimal validation** - Hanya validasi struktur message (jobId, userId, userEmail)
- ✅ **Trust assessment data** - Data assessment sudah divalidasi di Assessment Service
- ✅ **Performance optimized** - Tidak ada redundant validation
- ✅ **Focus on AI processing** - Lebih fokus pada core functionality

## Tidak Memiliki HTTP Server
Worker ini adalah background service yang tidak memiliki API publik.

## ✅ Implementation Status

**COMPLETED** - Analysis Worker telah diimplementasikan dengan fitur lengkap:

### Fitur yang Telah Diimplementasikan:
- ✅ RabbitMQ Consumer untuk mengonsumsi job dari assessment-service
- ✅ Google Generative AI integration untuk analisis persona
- ✅ Archive Service integration untuk menyimpan hasil analisis
- ✅ Notification Service integration untuk mengirim notifikasi
- ✅ Error handling dan retry mechanism
- ✅ Logging dan monitoring
- ✅ Optimized validation untuk job message structure dan persona profile
- ✅ Unit tests dan integration tests


### Struktur Proyek:
```
analysis-worker/
├── src/
│   ├── config/
│   │   ├── ai.js              # Konfigurasi Google AI
│   │   └── rabbitmq.js        # Konfigurasi RabbitMQ
│   ├── processors/
│   │   └── assessmentProcessor.js  # Processor utama untuk assessment
│   ├── schemas/
│   │   └── personaProfile.js  # Schema validasi persona profile
│   ├── services/
│   │   ├── aiService.js       # Service untuk Google AI
│   │   ├── archiveService.js  # Service untuk Archive Service
│   │   ├── notificationService.js  # Service untuk Notification Service
│   │   └── queueConsumer.js   # Service untuk RabbitMQ consumer
│   ├── utils/
│   │   ├── errorHandler.js    # Error handling utilities
│   │   ├── logger.js          # Logging utilities
│   │   ├── shutdown.js        # Graceful shutdown utilities
│   │   └── validator.js       # Data validation utilities
│   └── worker.js              # Entry point utama
├── tests/
│   ├── integration/
│   │   └── worker.test.js     # Integration tests
│   └── unit/
│       ├── aiService.test.js  # Unit tests untuk AI service
│       └── archiveService.test.js  # Unit tests untuk Archive service
├── .env.example               # Template environment variables
├── package.json               # Dependencies dan scripts
└── README.md                  # Dokumentasi
```

## Dependencies

```json
{
  "amqplib": "^0.10.3",
  "@google/generative-ai": "^0.2.1",
  "axios": "^1.4.0",
  "dotenv": "^16.0.3",
  "winston": "^3.8.2"
}
```

## Environment Variables

```env
NODE_ENV=development

# RabbitMQ Configuration
RABBITMQ_URL=amqp://localhost:5672
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
QUEUE_NAME=assessment_analysis
EXCHANGE_NAME=atma_exchange
ROUTING_KEY=analysis.process

# Google Generative AI Configuration
GOOGLE_AI_API_KEY=your_google_ai_api_key_here
GOOGLE_AI_MODEL=gemini-1.5-pro

# Mock AI Configuration (for testing without using paid Gemini API)
# Set to 'true' to use mock AI responses instead of real Gemini API
USE_MOCK_MODEL=false

# Archive Service Configuration
ARCHIVE_SERVICE_URL=http://localhost:3002
ARCHIVE_SERVICE_KEY=internal_service_secret_key_change_in_production

# Notification Service Configuration
NOTIFICATION_SERVICE_URL=http://localhost:3005
NOTIFICATION_SERVICE_KEY=internal_service_secret_key_change_in_production

# Worker Configuration
WORKER_CONCURRENCY=3
MAX_RETRIES=3
RETRY_DELAY=5000
PROCESSING_TIMEOUT=300000

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/analysis-worker.log
```

## Message Queue Integration

### Consumed Message Format
```json
{
  "jobId": "550e8400-e29b-41d4-a716-************",
  "userId": "550e8400-e29b-41d4-a716-************",
  "assessmentData": {
    "riasec": {
      "realistic": 75,
      "investigative": 85,
      "artistic": 60,
      "social": 50,
      "enterprising": 70,
      "conventional": 55
    },
    "ocean": {
      "openness": 80,
      "conscientiousness": 65,
      "extraversion": 55,
      "agreeableness": 45,
      "neuroticism": 30
    },
    "viaIs": {
      "creativity": 85,
      "curiosity": 78,
      "judgment": 70,
      "loveOfLearning": 82,
      "perspective": 60,
      "bravery": 55,
      "perseverance": 68,
      "honesty": 73,
      "zest": 66,
      "love": 80,
      "kindness": 75,
      "socialIntelligence": 65,
      "teamwork": 60,
      "fairness": 70,
      "leadership": 67,
      "forgiveness": 58,
      "humility": 62,
      "prudence": 69,
      "selfRegulation": 61,
      "appreciationOfBeauty": 50,
      "gratitude": 72,
      "hope": 77,
      "humor": 65,
      "spirituality": 55
    },

  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "priority": 1
}
```

## AI Prompt Engineering

### Prompt Template Structure
```
Anda adalah seorang psikolog profesional yang ahli dalam analisis kepribadian dan pemetaan bakat. 
Berdasarkan hasil assessment berikut, buatlah analisis persona yang komprehensif:

RIASEC Assessment:
- Realistic: {realistic}/100
- Investigative: {investigative}/100
- Artistic: {artistic}/100
- Social: {social}/100
- Enterprising: {enterprising}/100
- Conventional: {conventional}/100

OCEAN Personality Assessment:
- Openness: {openness}/100
- Conscientiousness: {conscientiousness}/100
- Extraversion: {extraversion}/100
- Agreeableness: {agreeableness}/100
- Neuroticism: {neuroticism}/100

VIA Character Strengths:
[24 character strengths dengan nilai masing-masing]

Multiple Intelligences:
[8 jenis kecerdasan dengan nilai masing-masing]

Cognitive Style Index:
- Analytic: {analytic}/100
- Intuitive: {intuitive}/100

Berdasarkan data di atas, buatlah analisis dalam format JSON yang sesuai dengan schema berikut:
[JSON Schema untuk persona_profile]

Pastikan analisis Anda:
1. Akurat berdasarkan data assessment
2. Memberikan insight yang mendalam dan actionable
3. Rekomendasi karir yang realistis dan sesuai
4. Menggunakan bahasa Indonesia yang profesional
5. Memberikan role model yang relevan dan inspiratif
```

## API Calls to External Services

### Archive Service - Save Result
**POST** `{ARCHIVE_SERVICE_URL}/archive/results`

**Headers:**
```
X-Internal-Service: true
X-Service-Key: {ARCHIVE_SERVICE_KEY}
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "550e8400-e29b-41d4-a716-************",
  "assessment_data": {...},
  "persona_profile": [...],
  "status": "completed"
}
```

### Notification Service - Send Completion Event
**POST** `{NOTIFICATION_SERVICE_URL}/notifications/analysis-complete`

**Headers:**
```
X-Internal-Service: true
X-Service-Key: {NOTIFICATION_SERVICE_KEY}
Content-Type: application/json
```

**Request Body:**
```json
{
  "userId": "550e8400-e29b-41d4-a716-************",
  "jobId": "550e8400-e29b-41d4-a716-************",
  "resultId": "550e8400-e29b-41d4-a716-446655440002",
  "status": "completed"
}
```

## Processing Flow

1. **Consume Message**: Ambil job dari RabbitMQ queue
2. **Validate Data**: Validasi struktur data assessment
3. **Build Prompt**: Susun prompt untuk AI berdasarkan data
4. **Call AI Service**: Kirim prompt ke Google Generative AI
5. **Parse Response**: Parse dan validasi response JSON dari AI
6. **Save to Archive**: Simpan hasil ke Archive Service
7. **Send Notification**: Kirim notifikasi completion
8. **Acknowledge Message**: Acknowledge message di queue
9. **Error Handling**: Jika error, retry atau send ke dead letter queue

## Error Handling

### Retry Strategy
- **Max Retries**: 3 attempts
- **Retry Delay**: 5 seconds (exponential backoff)
- **Timeout**: 5 minutes per job

### Error Types
1. **AI Service Error**: Retry dengan exponential backoff
2. **Archive Service Error**: Retry dengan linear backoff
3. **Validation Error**: Send to dead letter queue (no retry)
4. **Network Error**: Retry dengan exponential backoff

### Dead Letter Queue
Jobs yang gagal setelah max retries akan dikirim ke dead letter queue untuk manual investigation.

## Monitoring & Logging

### Log Events
- Job received from queue
- AI analysis started
- AI analysis completed
- Result saved to archive
- Notification sent
- Job completed successfully
- Error occurred (with details)

### Metrics
- Jobs processed per minute
- Average processing time
- Success rate
- Error rate by type
- Queue length

### Health Monitoring
Worker akan log heartbeat setiap 30 detik untuk monitoring.

## Scaling

### Horizontal Scaling
Multiple worker instances dapat dijalankan secara paralel:
```bash
# Terminal 1
npm start

# Terminal 2
npm start

# Terminal 3
npm start
```

### Configuration
- `WORKER_CONCURRENCY`: Jumlah job yang diproses bersamaan per worker
- Recommended: 1-3 concurrent jobs per worker instance
- Total throughput = workers × concurrency

## 🤖 Mock AI Model for Testing

Analysis Worker menyediakan **Mock AI Model** untuk testing tanpa menggunakan Gemini API yang berbayar.

### Quick Start dengan Mock AI:
```bash
# Start worker dengan mock AI model
npm run dev:mock

# Atau untuk production mode
npm run start:mock
```

### Fitur Mock AI Model:
- ✅ **Zero Cost**: Tidak menggunakan Gemini API yang berbayar
- ✅ **Offline Development**: Bekerja tanpa koneksi internet
- ✅ **Dynamic Responses**: Generate response berdasarkan assessment data
- ✅ **Valid Output**: Menghasilkan output yang sesuai dengan schema validation
- ✅ **Realistic Simulation**: Simulasi processing delay seperti real AI

### Configuration:
```env
# Set di .env file
USE_MOCK_MODEL=true
```

### Untuk dokumentasi lengkap Mock AI, lihat: [MOCK-AI-GUIDE.md](./MOCK-AI-GUIDE.md)

## Security Features

1. **API Key Protection**: Google AI API key disimpan sebagai environment variable
2. **Internal Service Authentication**: Menggunakan service key untuk komunikasi internal
3. **Input Validation**: Validasi data assessment sebelum processing
4. **Error Sanitization**: Error messages tidak mengekspos sensitive data
5. **Timeout Protection**: Timeout untuk mencegah hanging processes

## 🔗 Integrasi dengan Microservices

### 1. Assessment Service Integration
Analysis Worker mengonsumsi job dari RabbitMQ queue yang dikirim oleh Assessment Service:

**Queue Configuration:**
- Exchange: `atma_exchange` (type: direct)
- Queue: `assessment_analysis`
- Routing Key: `analysis.process`

**Message Format dari Assessment Service:**
```json
{
  "jobId": "uuid",
  "userId": "uuid",
  "userEmail": "<EMAIL>",
  "assessmentData": {
    "riasec": { ... },
    "ocean": { ... },
    "viaIs": { ... },
    "multipleIntelligences": { ... },
    "cognitiveStyleIndex": { ... }
  },
  "timestamp": "2024-01-01T00:00:00.000Z",
  "retryCount": 0
}
```

### 2. Archive Service Integration
Analysis Worker menyimpan hasil analisis ke Archive Service:

**Endpoint:** `POST /archive/results`
**Headers:**
```
X-Internal-Service: true
X-Service-Key: {ARCHIVE_SERVICE_KEY}
Content-Type: application/json
```

**Request Body:**
```json
{
  "user_id": "uuid",
  "assessment_data": { ... },
  "persona_profile": [ ... ],
  "status": "completed"
}
```

### 3. Notification Service Integration
Analysis Worker mengirim notifikasi setelah analisis selesai:

**Endpoint:** `POST /notifications/analysis-complete`
**Headers:**
```
X-Internal-Service: true
X-Service-Key: {NOTIFICATION_SERVICE_KEY}
Content-Type: application/json
```

**Request Body:**
```json
{
  "userId": "uuid",
  "jobId": "uuid",
  "resultId": "uuid",
  "status": "completed"
}
```

## Development & Testing

### Local Development
```bash
# Install dependencies
npm install

# Setup environment
cp .env.example .env
# Edit .env dengan konfigurasi yang sesuai

# Start worker
npm run dev
```

### Testing
```bash
# Unit tests
npm test

# Integration tests (requires RabbitMQ and services)
npm run test:integration

# Run tests with coverage
npm test -- --coverage
```

### Manual Testing
Untuk testing manual, gunakan Assessment Service untuk submit job, kemudian monitor logs worker.
