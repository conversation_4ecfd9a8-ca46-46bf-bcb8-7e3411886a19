/**
 * Mock AI Service for Testing
 * 
 * Provides mock responses for AI analysis to avoid using paid Gemini API during testing
 */

const logger = require('../utils/logger');

/**
 * Generate mock persona profile based on assessment data
 * @param {Object} assessmentData - Assessment data
 * @param {String} jobId - Job ID for logging
 * @returns {Promise<Object>} - Mock persona profile
 */
const generateMockPersonaProfile = async (assessmentData, jobId) => {
  try {
    logger.info('Generating mock persona profile', { jobId });

    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));

    // Extract assessment data for dynamic mock generation
    const { riasec, ocean, viaIs } = assessmentData;

    // Determine archetype based on highest RIASEC scores
    const riasecEntries = Object.entries(riasec);
    const topRiasec = riasecEntries.reduce((a, b) => riasec[a[0]] > riasec[b[0]] ? a : b);
    const secondRiasec = riasecEntries.filter(entry => entry[0] !== topRiasec[0])
      .reduce((a, b) => riasec[a[0]] > riasec[b[0]] ? a : b);

    // Generate archetype based on top RIASEC combinations
    const archetypeMap = {
      'realistic-investigative': 'The Practical Analyst',
      'realistic-conventional': 'The Systematic Builder',
      'investigative-artistic': 'The Creative Researcher',
      'investigative-realistic': 'The Technical Problem Solver',
      'artistic-social': 'The Creative Communicator',
      'artistic-investigative': 'The Innovative Thinker',
      'social-enterprising': 'The People Leader',
      'social-artistic': 'The Empathetic Creator',
      'enterprising-social': 'The Charismatic Leader',
      'enterprising-conventional': 'The Strategic Organizer',
      'conventional-realistic': 'The Detail-Oriented Implementer',
      'conventional-investigative': 'The Methodical Analyst'
    };

    const archetypeKey = `${topRiasec[0]}-${secondRiasec[0]}`;
    const archetype = archetypeMap[archetypeKey] || 'The Balanced Professional';

    // Generate strengths based on top scores
    const strengths = generateStrengths(riasec, ocean, viaIs);
    
    // Generate weaknesses based on low scores
    const weaknesses = generateWeaknesses(riasec, ocean, viaIs);

    // Generate career recommendations based on RIASEC profile
    const careerRecommendation = generateCareerRecommendations(riasec, ocean);

    // Generate insights based on personality profile
    const insights = generateInsights(riasec, ocean, viaIs);

    // Generate work environment based on personality
    const workEnvironment = generateWorkEnvironment(riasec, ocean);

    // Generate role models based on archetype and interests
    const roleModel = generateRoleModels(topRiasec[0], secondRiasec[0]);

    // Generate short summary
    const shortSummary = generateShortSummary(archetype, topRiasec, ocean);

    const mockProfile = {
      archetype,
      shortSummary,
      strengths,
      weaknesses,
      careerRecommendation,
      insights,
      workEnvironment,
      roleModel
    };

    logger.info('Mock persona profile generated successfully', {
      jobId,
      archetype,
      strengthsCount: strengths.length,
      weaknessesCount: weaknesses.length,
      careerCount: careerRecommendation.length
    });

    return mockProfile;

  } catch (error) {
    logger.error('Failed to generate mock persona profile', {
      jobId,
      error: error.message
    });
    throw error;
  }
};

/**
 * Generate strengths based on assessment scores
 */
const generateStrengths = (riasec, ocean, viaIs) => {
  const strengths = [];
  
  // RIASEC-based strengths
  if (riasec.realistic > 70) strengths.push('Kemampuan praktis dan hands-on yang kuat');
  if (riasec.investigative > 70) strengths.push('Kemampuan analitis dan problem-solving yang excellent');
  if (riasec.artistic > 70) strengths.push('Kreativitas dan kemampuan berpikir out-of-the-box');
  if (riasec.social > 70) strengths.push('Kemampuan interpersonal dan empati yang tinggi');
  if (riasec.enterprising > 70) strengths.push('Leadership dan kemampuan persuasi yang natural');
  if (riasec.conventional > 70) strengths.push('Organisasi dan attention to detail yang sangat baik');

  // OCEAN-based strengths
  if (ocean.openness > 70) strengths.push('Keterbukaan terhadap ide baru dan pengalaman');
  if (ocean.conscientiousness > 70) strengths.push('Disiplin dan reliability yang tinggi');
  if (ocean.extraversion > 70) strengths.push('Energi sosial dan kemampuan networking yang kuat');
  if (ocean.agreeableness > 70) strengths.push('Kemampuan kerjasama dan diplomasi');

  // VIA-IS based strengths (top 3)
  const viaEntries = Object.entries(viaIs).sort((a, b) => b[1] - a[1]).slice(0, 3);
  viaEntries.forEach(([strength, score]) => {
    if (score > 75) {
      const strengthMap = {
        creativity: 'Kreativitas dan inovasi yang luar biasa',
        curiosity: 'Rasa ingin tahu dan semangat belajar yang tinggi',
        judgment: 'Critical thinking dan kemampuan evaluasi yang tajam',
        loveOfLearning: 'Passion untuk pembelajaran berkelanjutan',
        perseverance: 'Ketekunan dan daya tahan yang kuat',
        leadership: 'Kemampuan memimpin dan menginspirasi orang lain'
      };
      if (strengthMap[strength]) {
        strengths.push(strengthMap[strength]);
      }
    }
  });

  return strengths.slice(0, 5); // Limit to 5 strengths
};

/**
 * Generate weaknesses based on low assessment scores
 */
const generateWeaknesses = (riasec, ocean, viaIs) => {
  const weaknesses = [];

  // OCEAN-based weaknesses
  if (ocean.neuroticism > 60) weaknesses.push('Kecenderungan stress dan anxiety dalam situasi pressure');
  if (ocean.conscientiousness < 40) weaknesses.push('Kesulitan dalam manajemen waktu dan organisasi');
  if (ocean.extraversion < 40) weaknesses.push('Kurang nyaman dalam situasi sosial yang intens');
  if (ocean.agreeableness < 40) weaknesses.push('Kesulitan dalam kompromi dan diplomasi');
  if (ocean.openness < 40) weaknesses.push('Resistensi terhadap perubahan dan ide-ide baru');

  // RIASEC-based weaknesses
  if (riasec.social < 40) weaknesses.push('Kurang optimal dalam peran yang membutuhkan interaksi sosial intensif');
  if (riasec.conventional < 40) weaknesses.push('Kesulitan mengikuti prosedur dan sistem yang rigid');
  if (riasec.enterprising < 40) weaknesses.push('Kurang nyaman dalam peran leadership dan sales');

  // VIA-IS based weaknesses (bottom 3)
  const viaEntries = Object.entries(viaIs).sort((a, b) => a[1] - b[1]).slice(0, 3);
  viaEntries.forEach(([weakness, score]) => {
    if (score < 40) {
      const weaknessMap = {
        selfRegulation: 'Kesulitan dalam self-control dan manajemen emosi',
        prudence: 'Kecenderungan impulsif dalam pengambilan keputusan',
        humility: 'Kesulitan menerima feedback dan mengakui keterbatasan',
        forgiveness: 'Kesulitan move on dari konflik atau kekecewaan'
      };
      if (weaknessMap[weakness]) {
        weaknesses.push(weaknessMap[weakness]);
      }
    }
  });

  // Ensure minimum 3 weaknesses by adding generic ones if needed
  if (weaknesses.length < 3) {
    const genericWeaknesses = [
      'Perlu pengembangan dalam area komunikasi interpersonal',
      'Dapat meningkatkan fleksibilitas dalam menghadapi perubahan',
      'Membutuhkan pengembangan dalam manajemen waktu yang lebih efektif',
      'Perlu peningkatan dalam kemampuan delegasi dan teamwork',
      'Dapat mengembangkan patience dalam menghadapi proses yang lambat'
    ];

    // Add generic weaknesses until we have at least 3
    for (let i = 0; i < genericWeaknesses.length && weaknesses.length < 3; i++) {
      if (!weaknesses.includes(genericWeaknesses[i])) {
        weaknesses.push(genericWeaknesses[i]);
      }
    }
  }

  return weaknesses.slice(0, 5); // Limit to 5 weaknesses
};

/**
 * Generate career recommendations based on RIASEC and OCEAN
 */
const generateCareerRecommendations = (riasec, ocean) => {
  const careers = [];

  // Get top 2 RIASEC interests
  const riasecEntries = Object.entries(riasec).sort((a, b) => b[1] - a[1]).slice(0, 2);
  const topInterests = riasecEntries.map(entry => entry[0]);

  // Career mapping based on RIASEC combinations
  const careerMap = {
    'realistic': ['Software Engineer', 'Data Scientist', 'Mechanical Engineer', 'Architect'],
    'investigative': ['Research Scientist', 'Data Analyst', 'Psychologist', 'Medical Doctor'],
    'artistic': ['UX/UI Designer', 'Content Creator', 'Marketing Creative', 'Product Designer'],
    'social': ['Human Resources Manager', 'Teacher', 'Counselor', 'Social Worker'],
    'enterprising': ['Business Development Manager', 'Sales Director', 'Entrepreneur', 'Project Manager'],
    'conventional': ['Financial Analyst', 'Operations Manager', 'Quality Assurance', 'Administrative Manager']
  };

  // Generate careers based on top interests
  topInterests.forEach(interest => {
    if (careerMap[interest]) {
      careerMap[interest].forEach(career => {
        if (!careers.find(c => c.careerName === career)) {
          careers.push({
            careerName: career,
            careerProspect: generateCareerProspect(career, riasec, ocean)
          });
        }
      });
    }
  });

  return careers.slice(0, 4); // Limit to 4 careers
};

/**
 * Generate career prospect for a specific career
 */
const generateCareerProspect = (career, riasec, ocean) => {
  // Base prospects for different career types
  const techCareers = ['Software Engineer', 'Data Scientist', 'Data Analyst', 'UX/UI Designer'];
  const businessCareers = ['Business Development Manager', 'Sales Director', 'Project Manager'];
  const creativeCareers = ['Content Creator', 'Marketing Creative', 'Product Designer'];
  const serviceCareers = ['Teacher', 'Counselor', 'Human Resources Manager'];

  let baseProspect = {
    jobAvailability: 'moderate',
    salaryPotential: 'moderate',
    careerProgression: 'moderate',
    industryGrowth: 'moderate',
    skillDevelopment: 'moderate'
  };

  if (techCareers.includes(career)) {
    baseProspect = {
      jobAvailability: 'high',
      salaryPotential: 'high',
      careerProgression: 'high',
      industryGrowth: 'super high',
      skillDevelopment: 'super high'
    };
  } else if (businessCareers.includes(career)) {
    baseProspect = {
      jobAvailability: 'high',
      salaryPotential: 'high',
      careerProgression: 'super high',
      industryGrowth: 'high',
      skillDevelopment: 'high'
    };
  } else if (creativeCareers.includes(career)) {
    baseProspect = {
      jobAvailability: 'moderate',
      salaryPotential: 'moderate',
      careerProgression: 'moderate',
      industryGrowth: 'high',
      skillDevelopment: 'high'
    };
  } else if (serviceCareers.includes(career)) {
    baseProspect = {
      jobAvailability: 'moderate',
      salaryPotential: 'moderate',
      careerProgression: 'moderate',
      industryGrowth: 'moderate',
      skillDevelopment: 'moderate'
    };
  }

  return baseProspect;
};

/**
 * Generate insights based on personality profile
 */
const generateInsights = (riasec, ocean, viaIs) => {
  const insights = [];

  // Conscientiousness insights
  if (ocean.conscientiousness > 70) {
    insights.push('Manfaatkan kekuatan organisasi Anda dengan mengambil peran yang membutuhkan planning dan execution yang detail');
  } else if (ocean.conscientiousness < 40) {
    insights.push('Fokus pada pengembangan time management dan organizational skills untuk meningkatkan produktivitas');
  }

  // Openness insights
  if (ocean.openness > 70) {
    insights.push('Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten');
  }

  // Extraversion insights
  if (ocean.extraversion > 70) {
    insights.push('Manfaatkan energy sosial Anda dalam peran yang melibatkan networking dan team collaboration');
  } else if (ocean.extraversion < 40) {
    insights.push('Pilih peran yang memungkinkan deep work dan interaksi sosial yang terbatas namun meaningful');
  }

  // RIASEC insights
  const topRiasec = Object.entries(riasec).reduce((a, b) => riasec[a[0]] > riasec[b[0]] ? a : b);
  if (topRiasec[0] === 'investigative') {
    insights.push('Kembangkan kemampuan research dan analytical thinking melalui proyek-proyek yang challenging');
  }

  // VIA-IS insights
  const topVia = Object.entries(viaIs).reduce((a, b) => viaIs[a[0]] > viaIs[b[0]] ? a : b);
  if (topVia[0] === 'creativity') {
    insights.push('Alokasikan waktu regular untuk creative exploration dan brainstorming sessions');
  }

  return insights.slice(0, 4); // Limit to 4 insights
};

/**
 * Generate work environment description
 */
const generateWorkEnvironment = (riasec, ocean) => {
  let environment = 'Lingkungan kerja ideal Anda adalah ';

  if (ocean.extraversion > 60) {
    environment += 'yang collaborative dan dynamic, dengan banyak interaksi tim dan networking opportunities. ';
  } else {
    environment += 'yang memungkinkan fokus mendalam dengan minimal distraction, namun tetap supportive. ';
  }

  if (riasec.investigative > 60) {
    environment += 'Anda akan thriving di tempat yang menghargai analytical thinking dan problem-solving, ';
  }

  if (riasec.artistic > 60) {
    environment += 'dengan kebebasan untuk eksplorasi kreatif dan inovasi. ';
  }

  if (ocean.conscientiousness > 60) {
    environment += 'Struktur yang jelas dan goal-oriented akan membantu Anda perform optimal, ';
  }

  environment += 'dengan leadership yang memberikan autonomy namun tetap available untuk guidance ketika dibutuhkan.';

  return environment;
};

/**
 * Generate role models based on interests
 */
const generateRoleModels = (topInterest, secondInterest) => {
  const roleModelMap = {
    'realistic': ['Elon Musk', 'Steve Wozniak', 'James Dyson'],
    'investigative': ['Marie Curie', 'Stephen Hawking', 'Jane Goodall'],
    'artistic': ['Steve Jobs', 'Jony Ive', 'David Kelley'],
    'social': ['Oprah Winfrey', 'Nelson Mandela', 'Mother Teresa'],
    'enterprising': ['Richard Branson', 'Jack Ma', 'Sara Blakely'],
    'conventional': ['Warren Buffett', 'Tim Cook', 'Sheryl Sandberg']
  };

  const roleModels = [];
  
  if (roleModelMap[topInterest]) {
    roleModels.push(...roleModelMap[topInterest]);
  }
  
  if (roleModelMap[secondInterest] && secondInterest !== topInterest) {
    roleModels.push(...roleModelMap[secondInterest].slice(0, 2));
  }

  // Add some universal role models
  const universalModels = ['Bill Gates', 'Michelle Obama'];
  roleModels.push(...universalModels);

  return [...new Set(roleModels)].slice(0, 5); // Remove duplicates and limit to 5
};

/**
 * Generate short summary
 */
const generateShortSummary = (archetype, topRiasec, ocean) => {
  let summary = `Anda adalah seorang ${archetype} dengan kekuatan utama di bidang ${topRiasec[0]}. `;
  
  if (ocean.conscientiousness > 60) {
    summary += 'Kepribadian yang terorganisir dan goal-oriented membuat Anda reliable dalam execution. ';
  }
  
  if (ocean.openness > 60) {
    summary += 'Keterbukaan terhadap pengalaman baru memungkinkan Anda untuk terus berkembang dan beradaptasi. ';
  }
  
  summary += 'Kombinasi unik dari analytical thinking dan practical approach membuat Anda valuable dalam berbagai konteks profesional.';
  
  return summary;
};

module.exports = {
  generateMockPersonaProfile
};
