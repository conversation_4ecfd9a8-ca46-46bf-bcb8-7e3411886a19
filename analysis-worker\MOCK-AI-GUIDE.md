# Mock AI Model Guide

Panduan lengkap untuk menggunakan Mock AI Model dalam Analysis Worker untuk testing tanpa menggunakan Gemini API yang berbayar.

## 🎯 Tujuan Mock AI Model

Mock AI Model dibuat untuk:
- **Testing tanpa biaya**: Menghindari penggunaan Gemini API yang berbayar selama development dan testing
- **Konsistensi testing**: Memberikan hasil yang predictable untuk unit testing
- **Development offline**: Memungkinkan development tanpa koneksi internet ke Google AI
- **Performance testing**: Menguji throughput worker tanpa batasan API rate limit

## 🚀 Cara Menggunakan Mock Model

### 1. Quick Start - Menggunakan Environment Testing

Cara tercepat untuk menggunakan mock model:

```bash
# Menggunakan script yang sudah disediakan
npm run dev:mock

# Atau untuk production mode
npm run start:mock
```

Script ini akan otomatis:
- Copy file `.env.testing` ke `.env`
- Set `USE_MOCK_MODEL=true`
- Start worker dengan mock AI model

### 2. Manual Configuration

Jika ingin mengatur manual:

```bash
# 1. Copy environment template
cp .env.example .env

# 2. Edit .env file
# Set USE_MOCK_MODEL=true
```

Edit file `.env`:
```env
# Mock AI Configuration - ENABLED FOR TESTING
USE_MOCK_MODEL=true

# Google AI API Key tidak diperlukan saat menggunakan mock
GOOGLE_AI_API_KEY=test_api_key_placeholder
```

### 3. Environment Variable Configuration

| Variable | Value | Description |
|----------|-------|-------------|
| `USE_MOCK_MODEL` | `true` | Mengaktifkan mock AI model |
| `USE_MOCK_MODEL` | `false` | Menggunakan real Gemini API |
| `GOOGLE_AI_API_KEY` | `test_api_key_placeholder` | Fallback untuk mock mode |

## 🔧 Fitur Mock AI Model

### 1. Dynamic Response Generation

Mock model tidak memberikan response static, tetapi menghasilkan response dinamis berdasarkan:

- **RIASEC Profile**: Archetype dan career recommendations disesuaikan dengan skor RIASEC tertinggi
- **OCEAN Personality**: Strengths, weaknesses, dan work environment disesuaikan dengan traits
- **VIA Character Strengths**: Insights dan role models disesuaikan dengan character strengths tertinggi

### 2. Realistic Processing Simulation

- **Processing Delay**: Simulasi delay 1-3 detik untuk meniru real AI processing
- **Variability**: Response time bervariasi untuk simulasi yang realistis
- **Logging**: Log yang sama dengan real AI service untuk consistency

### 3. Valid Output Structure

Mock model menghasilkan output yang:
- ✅ Sesuai dengan schema validation yang sama dengan real AI
- ✅ Memiliki struktur JSON yang identik
- ✅ Lulus semua validation checks
- ✅ Compatible dengan Archive Service

## 📊 Contoh Output Mock Model

### Input Assessment Data:
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  }
}
```

### Output Mock Persona Profile:
```json
{
  "archetype": "The Practical Analyst",
  "shortSummary": "Anda adalah seorang The Practical Analyst dengan kekuatan utama di bidang investigative...",
  "strengths": [
    "Kemampuan analitis dan problem-solving yang excellent",
    "Kemampuan praktis dan hands-on yang kuat",
    "Keterbukaan terhadap ide baru dan pengalaman"
  ],
  "weaknesses": [
    "Kurang optimal dalam peran yang membutuhkan interaksi sosial intensif",
    "Kesulitan dalam kompromi dan diplomasi"
  ],
  "careerRecommendation": [
    {
      "careerName": "Data Scientist",
      "careerProspect": {
        "jobAvailability": "high",
        "salaryPotential": "high",
        "careerProgression": "high",
        "industryGrowth": "super high",
        "skillDevelopment": "super high"
      }
    }
  ],
  "insights": [
    "Manfaatkan kekuatan organisasi Anda dengan mengambil peran yang membutuhkan planning dan execution yang detail",
    "Cari lingkungan kerja yang memberikan variasi dan tantangan baru secara konsisten"
  ],
  "workEnvironment": "Lingkungan kerja ideal Anda adalah yang memungkinkan fokus mendalam...",
  "roleModel": ["Marie Curie", "Stephen Hawking", "Steve Jobs", "Bill Gates"]
}
```

## 🧪 Testing dengan Mock Model

### 1. Unit Testing

```bash
# Run unit tests untuk mock AI service
npm test -- tests/unit/mockAiService.test.js

# Run semua unit tests
npm test
```

### 2. Integration Testing

```bash
# Set environment untuk testing
export USE_MOCK_MODEL=true

# Run integration tests
npm run test:integration
```

### 3. Manual Testing

```bash
# Start worker dengan mock model
npm run dev:mock

# Submit test job melalui Assessment Service
# Worker akan memproses menggunakan mock AI
```

## 📈 Performance Comparison

| Aspect | Real Gemini API | Mock AI Model |
|--------|----------------|---------------|
| **Cost** | $0.001-0.01 per request | Free |
| **Speed** | 2-10 seconds | 1-3 seconds |
| **Rate Limit** | 60 requests/minute | Unlimited |
| **Offline** | ❌ Requires internet | ✅ Works offline |
| **Consistency** | Variable responses | Predictable patterns |

## 🔄 Switching Between Real and Mock

### Development Workflow:

1. **Development Phase**: Use mock model
   ```bash
   npm run dev:mock
   ```

2. **Testing Phase**: Use mock model for unit tests
   ```bash
   USE_MOCK_MODEL=true npm test
   ```

3. **Integration Testing**: Use real API for final validation
   ```bash
   USE_MOCK_MODEL=false npm run test:integration
   ```

4. **Production**: Use real API
   ```bash
   USE_MOCK_MODEL=false npm start
   ```

## 🛠 Customizing Mock Responses

Untuk mengkustomisasi mock responses, edit file `src/services/mockAiService.js`:

### 1. Menambah Archetype Baru:
```javascript
const archetypeMap = {
  'realistic-investigative': 'The Practical Analyst',
  'artistic-social': 'The Creative Communicator',
  // Tambah archetype baru di sini
  'your-combination': 'Your Custom Archetype'
};
```

### 2. Menambah Career Options:
```javascript
const careerMap = {
  'realistic': ['Software Engineer', 'Data Scientist', 'Your New Career'],
  // Tambah career options di sini
};
```

### 3. Menambah Role Models:
```javascript
const roleModelMap = {
  'investigative': ['Marie Curie', 'Stephen Hawking', 'Your Role Model'],
  // Tambah role models di sini
};
```

## 🚨 Troubleshooting

### Problem: Mock model tidak aktif
**Solution**: 
```bash
# Check environment variable
echo $USE_MOCK_MODEL

# Pastikan set ke 'true'
export USE_MOCK_MODEL=true
```

### Problem: Validation error pada mock response
**Solution**: 
```bash
# Run validation test
npm test -- tests/unit/mockAiService.test.js

# Check schema di src/schemas/personaProfile.js
```

### Problem: Mock response tidak sesuai assessment data
**Solution**: 
- Check logic di `generateStrengths()`, `generateWeaknesses()`, dll.
- Pastikan mapping RIASEC ke archetype sudah benar

## 📝 Best Practices

1. **Always test with mock first**: Develop dan test dengan mock model sebelum menggunakan real API
2. **Validate mock responses**: Pastikan mock responses lulus validation yang sama dengan real API
3. **Use realistic data**: Gunakan assessment data yang realistis untuk testing
4. **Monitor logs**: Check logs untuk memastikan mock model berjalan dengan benar
5. **Performance testing**: Gunakan mock model untuk load testing worker performance

## 🔗 Related Files

- `src/config/ai.js` - AI configuration dengan mock model support
- `src/services/aiService.js` - Main AI service dengan mock integration
- `src/services/mockAiService.js` - Mock AI implementation
- `.env.testing` - Environment configuration untuk testing
- `tests/unit/mockAiService.test.js` - Unit tests untuk mock service
