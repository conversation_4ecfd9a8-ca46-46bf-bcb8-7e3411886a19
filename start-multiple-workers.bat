@echo off
REM Script untuk menjalankan multiple analysis worker instances
REM Optimal untuk menangani 100 concurrent users

echo ========================================
echo Starting Multiple Analysis Workers
echo ========================================
echo.

REM Configuration
set WORKER_COUNT=6
set BASE_PORT=4000
set WORKER_CONCURRENCY=5

echo Configuration:
echo - Number of Workers: %WORKER_COUNT%
echo - Worker Concurrency: %WORKER_CONCURRENCY% jobs per worker
echo - Total Capacity: %WORKER_COUNT% x %WORKER_CONCURRENCY% = 30 concurrent jobs
echo.

REM Check if analysis-worker directory exists
if not exist "analysis-worker" (
    echo ERROR: analysis-worker directory not found
    echo Please run this script from the atma-backend root directory
    pause
    exit /b 1
)

REM Create logs directory if not exists
if not exist "analysis-worker\logs" (
    mkdir "analysis-worker\logs"
)

REM Kill existing worker processes (cleanup)
echo Cleaning up existing worker processes...
taskkill /f /im node.exe 2>nul
timeout /t 2 /nobreak >nul

echo Starting %WORKER_COUNT% worker instances...
echo.

REM Start worker instances
for /l %%i in (1,1,%WORKER_COUNT%) do (
    echo Starting Worker %%i...
    
    REM Set environment variables for this worker instance
    set WORKER_INSTANCE_ID=worker-%%i
    set WORKER_PORT=%BASE_PORT%%%i
    set LOG_FILE=logs/analysis-worker-%%i.log
    set WORKER_CONCURRENCY=%WORKER_CONCURRENCY%
    
    REM Start worker in new command window
    start "Analysis Worker %%i" cmd /k "cd analysis-worker && set WORKER_INSTANCE_ID=worker-%%i && set LOG_FILE=logs/analysis-worker-%%i.log && set WORKER_CONCURRENCY=%WORKER_CONCURRENCY% && npm start"
    
    REM Small delay between worker starts
    timeout /t 2 /nobreak >nul
)

echo.
echo ✅ All %WORKER_COUNT% workers started successfully!
echo.
echo Worker Status:
echo - Worker 1: analysis-worker-1.log
echo - Worker 2: analysis-worker-2.log  
echo - Worker 3: analysis-worker-3.log
echo - Worker 4: analysis-worker-4.log
echo - Worker 5: analysis-worker-5.log
echo - Worker 6: analysis-worker-6.log
echo.
echo Total Processing Capacity: 30 concurrent jobs
echo Estimated Processing Time for 100 jobs: 3-4 minutes
echo.
echo Monitoring Commands:
echo   tail -f analysis-worker\logs\analysis-worker-1.log
echo   node clear-queue.js (to clear queue if needed)
echo.
echo Press any key to show worker monitoring dashboard...
pause >nul

REM Show monitoring dashboard
:monitor
cls
echo ========================================
echo Analysis Workers Monitoring Dashboard
echo ========================================
echo.
echo Current Time: %date% %time%
echo.

REM Check worker processes
echo Worker Processes:
tasklist /fi "windowtitle eq Analysis Worker*" 2>nul | find "node.exe" >nul
if %errorlevel% equ 0 (
    echo ✅ Workers are running
) else (
    echo ❌ No workers found running
)

echo.
echo Recent Log Entries:
echo.

REM Show last few lines from each worker log
for /l %%i in (1,1,%WORKER_COUNT%) do (
    if exist "analysis-worker\logs\analysis-worker-%%i.log" (
        echo --- Worker %%i ---
        powershell "Get-Content 'analysis-worker\logs\analysis-worker-%%i.log' | Select-Object -Last 2"
        echo.
    )
)

echo.
echo Commands:
echo   R - Refresh dashboard
echo   S - Stop all workers  
echo   Q - Quit monitoring
echo.
set /p choice="Enter choice: "

if /i "%choice%"=="r" goto monitor
if /i "%choice%"=="s" goto stop_workers
if /i "%choice%"=="q" goto end

goto monitor

:stop_workers
echo.
echo Stopping all worker processes...
taskkill /f /im node.exe 2>nul
echo ✅ All workers stopped
echo.
pause
goto end

:end
echo.
echo Monitoring ended.
pause
