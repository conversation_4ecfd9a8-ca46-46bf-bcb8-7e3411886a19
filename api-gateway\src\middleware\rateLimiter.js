const rateLimit = require('express-rate-limit');
const logger = require('../utils/logger');

// Store untuk tracking rate limit per user
const userLimitStore = new Map();

/**
 * Custom key generator yang menggunakan user ID jika tersedia, atau IP address
 */
const keyGenerator = (req) => {
  if (req.user && req.user.id) {
    return `user:${req.user.id}`;
  }
  return `ip:${req.ip}`;
};

/**
 * Custom handler untuk rate limit exceeded
 */
const rateLimitHandler = (req, res) => {
  const key = keyGenerator(req);
  
  logger.warn(`Rate limit exceeded for ${key}`, {
    path: req.path,
    method: req.method,
    ip: req.ip,
    userAgent: req.get('User-Agent'),
    userId: req.user?.id
  });

  res.status(429).json({
    success: false,
    error: {
      code: 'RATE_LIMIT_EXCEEDED',
      message: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(10 * 60) // 10 minutes
    }
  });
};

/**
 * Rate limiter utama berdasarkan IP - 1000 requests per 10 minutes
 */
const generalRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 1000, // 1000 requests per 10 minutes
  message: rateLimitHandler,
  standardHeaders: true, // Return rate limit info in the `RateLimit-*` headers
  legacyHeaders: false, // Disable the `X-RateLimit-*` headers
  keyGenerator: (req) => req.ip,
  skip: (req) => {
    // Skip rate limiting for health checks
    return req.path === '/health';
  }
});

/**
 * Rate limiter khusus untuk endpoint authentication
 */
const authRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 1000, // 1000 auth requests per 10 minutes
  message: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: (req) => req.ip,
  skipSuccessfulRequests: true // Don't count successful requests
});

/**
 * Rate limiter khusus untuk endpoint assessment
 */
const assessmentRateLimit = rateLimit({
  windowMs: 10 * 60 * 1000, // 10 minutes
  max: 1000, // 1000 assessment requests per 10 minutes per user
  message: rateLimitHandler,
  standardHeaders: true,
  legacyHeaders: false,
  keyGenerator: keyGenerator,
  skip: (req) => {
    // Skip if not authenticated
    return !req.user;
  }
});

/**
 * Middleware untuk menambahkan rate limit headers
 */
const addRateLimitHeaders = (req, res, next) => {
  const originalSend = res.send;
  
  res.send = function(data) {
    // Add custom rate limit headers
    res.set({
      'X-RateLimit-Window': process.env.RATE_LIMIT_WINDOW_MS || '900000',
      'X-RateLimit-Policy': 'general'
    });
    
    return originalSend.call(this, data);
  };
  
  next();
};

module.exports = {
  generalRateLimit,
  authRateLimit,
  assessmentRateLimit,
  addRateLimitHeaders
};
