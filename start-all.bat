@echo off
title ATMA Backend Launcher

echo ========================================
echo ATMA Backend - Starting All Services
echo ========================================
echo.

REM Configuration
set WORKER_COUNT=5
set WORKER_CONCURRENCY=5

echo Configuration:
echo - Number of Analysis Workers: %WORKER_COUNT%
echo - Worker Concurrency: %WORKER_CONCURRENCY% jobs per worker
echo - Total Processing Capacity: %WORKER_COUNT% x %WORKER_CONCURRENCY% = 25 concurrent jobs
echo.

REM Check if all service directories exist
echo Checking service directories...
set MISSING_SERVICES=0

if not exist "api-gateway" (
    echo ERROR: api-gateway directory not found
    set MISSING_SERVICES=1
)

if not exist "auth-service" (
    echo ERROR: auth-service directory not found
    set MISSING_SERVICES=1
)

if not exist "archive-service" (
    echo ERROR: archive-service directory not found
    set MISSING_SERVICES=1
)

if not exist "assessment-service" (
    echo ERROR: assessment-service directory not found
    set MISSING_SERVICES=1
)

if not exist "notification-service" (
    echo ERROR: notification-service directory not found
    set MISSING_SERVICES=1
)

if not exist "analysis-worker" (
    echo ERROR: analysis-worker directory not found
    set MISSING_SERVICES=1
)

if %MISSING_SERVICES% equ 1 (
    echo.
    echo Please run this script from the atma-backend root directory
    pause
    exit /b 1
)

echo ✅ All service directories found
echo.

REM Create logs directories if not exist
echo Creating logs directories...
if not exist "api-gateway\logs" mkdir "api-gateway\logs"
if not exist "auth-service\logs" mkdir "auth-service\logs"
if not exist "archive-service\logs" mkdir "archive-service\logs"
if not exist "assessment-service\logs" mkdir "assessment-service\logs"
if not exist "notification-service\logs" mkdir "notification-service\logs"
if not exist "analysis-worker\logs" mkdir "analysis-worker\logs"

echo ✅ Logs directories ready
echo.

REM Kill existing processes (cleanup)
echo Cleaning up existing processes...
taskkill /f /im node.exe 2>nul
timeout /t 3 /nobreak >nul

echo ========================================
echo Starting Core Services...
echo ========================================

REM Start Auth Service (must be first - other services depend on it)
echo [1/6] Starting Auth Service...
start "Auth Service" cmd /k "cd /d %~dp0auth-service && npm start"
timeout /t 3 /nobreak >nul

REM Start Archive Service
echo [2/6] Starting Archive Service...
start "Archive Service" cmd /k "cd /d %~dp0archive-service && npm start"
timeout /t 3 /nobreak >nul

REM Start Assessment Service
echo [3/6] Starting Assessment Service...
start "Assessment Service" cmd /k "cd /d %~dp0assessment-service && npm start"
timeout /t 3 /nobreak >nul

REM Start Notification Service
echo [4/6] Starting Notification Service...
start "Notification Service" cmd /k "cd /d %~dp0notification-service && npm start"
timeout /t 3 /nobreak >nul

REM Start API Gateway (should be last among services)
echo [5/6] Starting API Gateway...
start "API Gateway" cmd /k "cd /d %~dp0api-gateway && npm start"
timeout /t 3 /nobreak >nul

echo [6/6] Core services started!
echo.

echo ========================================
echo Starting Analysis Workers...
echo ========================================

REM Start multiple analysis worker instances
for /l %%i in (1,1,%WORKER_COUNT%) do (
    echo Starting Analysis Worker %%i...

    REM Start worker in new command window with unique instance ID
    start "Analysis Worker %%i" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-%%i && set LOG_FILE=logs/analysis-worker-%%i.log && set WORKER_CONCURRENCY=%WORKER_CONCURRENCY% && npm start"

    REM Small delay between worker starts
    timeout /t 2 /nobreak >nul
)

echo.
echo ✅ All %WORKER_COUNT% analysis workers started!
echo.

echo ========================================
echo ATMA Backend - All Services Running
echo ========================================
echo.
echo Core Services:
echo - Auth Service (Port 3001)
echo - Archive Service (Port 3002)
echo - Assessment Service (Port 3003)
echo - Notification Service (Port 3005)
echo - API Gateway (Port 3000) - Main Entry Point
echo.
echo Analysis Workers:
echo - Worker 1: analysis-worker-1.log
echo - Worker 2: analysis-worker-2.log
echo - Worker 3: analysis-worker-3.log
echo - Worker 4: analysis-worker-4.log
echo - Worker 5: analysis-worker-5.log
echo.
echo Total Processing Capacity: 25 concurrent assessment jobs
echo.
echo Monitoring Commands:
echo   tail -f analysis-worker\logs\analysis-worker-1.log
echo   node clear-queue.js (to clear RabbitMQ queue if needed)
echo.
echo API Endpoints:
echo   http://localhost:3000/api/health (Health check)
echo   http://localhost:3000/api/auth/login (Login)
echo   http://localhost:3000/api/assessment/submit (Submit assessment)
echo.
echo Press any key to show monitoring dashboard...
pause >nul

REM Show monitoring dashboard
:monitor
cls
echo ========================================
echo ATMA Backend - Monitoring Dashboard
echo ========================================
echo.
echo Current Time: %date% %time%
echo.

REM Check service processes
echo Service Status:
echo Checking running Node.js processes...

REM Count total node processes
for /f %%i in ('tasklist /fi "imagename eq node.exe" 2^>nul ^| find /c "node.exe"') do set NODE_COUNT=%%i

if %NODE_COUNT% gtr 0 (
    echo Found %NODE_COUNT% Node.js processes running
    echo.
    echo Process Details:
    tasklist /fi "imagename eq node.exe" /fo table 2>nul | findstr /v "INFO:"
) else (
    echo No Node.js processes found running
    echo.
    echo This might indicate:
    echo - Services failed to start
    echo - Services are starting up (wait a moment)
    echo - Dependencies missing (check logs)
)

echo.
echo Worker Log Files:
for /l %%i in (1,1,%WORKER_COUNT%) do (
    if exist "analysis-worker\logs\analysis-worker-%%i.log" (
        echo - Worker %%i log exists: analysis-worker\logs\analysis-worker-%%i.log
    ) else (
        echo - Worker %%i log not found
    )
)

echo.
echo Port Status Check:
echo Checking if services are listening on their ports...
netstat -an | findstr ":3001 " >nul && echo Auth Service (3001) - Listening || echo Auth Service (3001) - Not Listening
netstat -an | findstr ":3002 " >nul && echo Archive Service (3002) - Listening || echo Archive Service (3002) - Not Listening
netstat -an | findstr ":3003 " >nul && echo Assessment Service (3003) - Listening || echo Assessment Service (3003) - Not Listening
netstat -an | findstr ":3005 " >nul && echo Notification Service (3005) - Listening || echo Notification Service (3005) - Not Listening
netstat -an | findstr ":3000 " >nul && echo API Gateway (3000) - Listening || echo API Gateway (3000) - Not Listening

echo.
echo Commands:
echo   R - Refresh dashboard
echo   S - Stop all services and workers
echo   W - Restart workers only
echo   Q - Quit monitoring
echo.
set /p choice="Enter choice: "

if /i "%choice%"=="r" goto monitor
if /i "%choice%"=="s" goto stop_all
if /i "%choice%"=="w" goto restart_workers
if /i "%choice%"=="q" goto end

goto monitor

:restart_workers
echo.
echo Stopping analysis workers...
for /l %%i in (1,1,%WORKER_COUNT%) do (
    taskkill /f /fi "windowtitle eq Analysis Worker %%i*" 2>nul
)
timeout /t 2 /nobreak >nul

echo Starting analysis workers...
for /l %%i in (1,1,%WORKER_COUNT%) do (
    echo Starting Analysis Worker %%i...
    start "Analysis Worker %%i" cmd /k "cd /d %~dp0analysis-worker && set WORKER_INSTANCE_ID=worker-%%i && set LOG_FILE=logs/analysis-worker-%%i.log && set WORKER_CONCURRENCY=%WORKER_CONCURRENCY% && npm start"
    timeout /t 2 /nobreak >nul
)
echo ✅ Workers restarted
timeout /t 3 /nobreak >nul
goto monitor

:stop_all
echo.
echo Stopping all services and workers...
taskkill /f /im node.exe 2>nul
echo ✅ All services and workers stopped
echo.
pause
goto end

:end
echo.
echo Monitoring ended.
echo.
echo To restart all services, run: start-all.bat
echo To start only workers, run: start-multiple-workers.bat
echo.
pause
