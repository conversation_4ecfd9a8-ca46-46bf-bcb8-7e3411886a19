@echo off
title Main Microservice Launcher

echo Starting all microservices...

REM Start API Gateway
start "API Gateway" cmd /c "cd /d %~dp0api-gateway && npm start"

REM Start Analysis Worker (if exists)
if exist "analysis-worker" (
    start "Analysis Worker" cmd /c "cd /d %~dp0analysis-worker && npm start"
)

REM Start Archive Service
start "Archive Service" cmd /c "cd /d %~dp0archive-service && npm start"

REM Start Assessment Service
start "Assessment Service" cmd /c "cd /d %~dp0assessment-service && npm start"

REM Start Auth Service
start "Auth Service" cmd /c "cd /d %~dp0auth-service && npm start"

REM Start Notification Service
start "Notification Service" cmd /c "cd /d %~dp0notification-service && npm start"

echo All services have been launched in separate windows.
echo.
echo Services started:
echo - API Gateway (Port 3000)
echo - Auth Service (Port 3001)
echo - Archive Service (Port 3002)
echo - Assessment Service (Port 3003)
echo - Notification Service (Port 3005)
echo.
echo Press any key to exit...
pause >nul
