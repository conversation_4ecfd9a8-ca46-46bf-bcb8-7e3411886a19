/**
 * Unit Tests for Mock AI Service
 */

const mockAiService = require('../../src/services/mockAiService');
const { validatePersonaProfile } = require('../../src/utils/validator');

describe('Mock AI Service', () => {
  const sampleAssessmentData = {
    riasec: {
      realistic: 75,
      investigative: 85,
      artistic: 60,
      social: 50,
      enterprising: 70,
      conventional: 55
    },
    ocean: {
      openness: 80,
      conscientiousness: 65,
      extraversion: 55,
      agreeableness: 45,
      neuroticism: 30
    },
    viaIs: {
      creativity: 85,
      curiosity: 78,
      judgment: 70,
      loveOfLearning: 82,
      perspective: 60,
      bravery: 55,
      perseverance: 68,
      honesty: 73,
      zest: 66,
      love: 80,
      kindness: 75,
      socialIntelligence: 65,
      teamwork: 60,
      fairness: 70,
      leadership: 67,
      forgiveness: 58,
      humility: 62,
      prudence: 69,
      selfRegulation: 61,
      appreciationOfBeauty: 50,
      gratitude: 72,
      hope: 77,
      humor: 65,
      spirituality: 55
    }
  };

  describe('generateMockPersonaProfile', () => {
    test('should generate valid persona profile', async () => {
      const jobId = 'test-job-123';
      const result = await mockAiService.generateMockPersonaProfile(sampleAssessmentData, jobId);

      // Validate structure
      expect(result).toHaveProperty('archetype');
      expect(result).toHaveProperty('shortSummary');
      expect(result).toHaveProperty('strengths');
      expect(result).toHaveProperty('weaknesses');
      expect(result).toHaveProperty('careerRecommendation');
      expect(result).toHaveProperty('insights');
      expect(result).toHaveProperty('workEnvironment');
      expect(result).toHaveProperty('roleModel');

      // Validate types
      expect(typeof result.archetype).toBe('string');
      expect(typeof result.shortSummary).toBe('string');
      expect(Array.isArray(result.strengths)).toBe(true);
      expect(Array.isArray(result.weaknesses)).toBe(true);
      expect(Array.isArray(result.careerRecommendation)).toBe(true);
      expect(Array.isArray(result.insights)).toBe(true);
      expect(typeof result.workEnvironment).toBe('string');
      expect(Array.isArray(result.roleModel)).toBe(true);

      // Validate array lengths
      expect(result.strengths.length).toBeGreaterThanOrEqual(3);
      expect(result.strengths.length).toBeLessThanOrEqual(5);
      expect(result.weaknesses.length).toBeGreaterThanOrEqual(3);
      expect(result.weaknesses.length).toBeLessThanOrEqual(5);
      expect(result.careerRecommendation.length).toBeGreaterThanOrEqual(3);
      expect(result.careerRecommendation.length).toBeLessThanOrEqual(5);
      expect(result.insights.length).toBeGreaterThanOrEqual(3);
      expect(result.insights.length).toBeLessThanOrEqual(5);
      expect(result.roleModel.length).toBeGreaterThanOrEqual(4);
      expect(result.roleModel.length).toBeLessThanOrEqual(5);
    });

    test('should generate career recommendations with proper structure', async () => {
      const jobId = 'test-job-456';
      const result = await mockAiService.generateMockPersonaProfile(sampleAssessmentData, jobId);

      result.careerRecommendation.forEach(career => {
        expect(career).toHaveProperty('careerName');
        expect(career).toHaveProperty('careerProspect');
        expect(typeof career.careerName).toBe('string');
        expect(typeof career.careerProspect).toBe('object');

        const prospect = career.careerProspect;
        expect(prospect).toHaveProperty('jobAvailability');
        expect(prospect).toHaveProperty('salaryPotential');
        expect(prospect).toHaveProperty('careerProgression');
        expect(prospect).toHaveProperty('industryGrowth');
        expect(prospect).toHaveProperty('skillDevelopment');

        // Validate enum values
        const validValues = ['super high', 'high', 'moderate', 'low', 'super low'];
        expect(validValues).toContain(prospect.jobAvailability);
        expect(validValues).toContain(prospect.salaryPotential);
        expect(validValues).toContain(prospect.careerProgression);
        expect(validValues).toContain(prospect.industryGrowth);
        expect(validValues).toContain(prospect.skillDevelopment);
      });
    });

    test('should pass persona profile validation', async () => {
      const jobId = 'test-job-789';
      const result = await mockAiService.generateMockPersonaProfile(sampleAssessmentData, jobId);

      const validationResult = validatePersonaProfile(result);
      expect(validationResult.isValid).toBe(true);
      if (!validationResult.isValid) {
        console.log('Validation error:', validationResult.error);
      }
    });

    test('should generate different archetypes for different RIASEC profiles', async () => {
      const artisticProfile = {
        ...sampleAssessmentData,
        riasec: {
          realistic: 30,
          investigative: 40,
          artistic: 90,
          social: 80,
          enterprising: 50,
          conventional: 20
        }
      };

      const conventionalProfile = {
        ...sampleAssessmentData,
        riasec: {
          realistic: 40,
          investigative: 50,
          artistic: 30,
          social: 40,
          enterprising: 60,
          conventional: 90
        }
      };

      const artisticResult = await mockAiService.generateMockPersonaProfile(artisticProfile, 'artistic-test');
      const conventionalResult = await mockAiService.generateMockPersonaProfile(conventionalProfile, 'conventional-test');

      // Should generate different archetypes
      expect(artisticResult.archetype).not.toBe(conventionalResult.archetype);
    });

    test('should handle processing delay simulation', async () => {
      const startTime = Date.now();
      await mockAiService.generateMockPersonaProfile(sampleAssessmentData, 'delay-test');
      const endTime = Date.now();
      
      // Should take at least 1 second (1000ms) due to simulated delay
      expect(endTime - startTime).toBeGreaterThanOrEqual(1000);
      // Should not take more than 4 seconds (reasonable upper bound)
      expect(endTime - startTime).toBeLessThan(4000);
    });

    test('should generate non-empty content for all fields', async () => {
      const result = await mockAiService.generateMockPersonaProfile(sampleAssessmentData, 'content-test');

      expect(result.archetype.length).toBeGreaterThan(0);
      expect(result.shortSummary.length).toBeGreaterThan(0);
      expect(result.workEnvironment.length).toBeGreaterThan(0);
      
      result.strengths.forEach(strength => {
        expect(strength.length).toBeGreaterThan(0);
      });
      
      result.weaknesses.forEach(weakness => {
        expect(weakness.length).toBeGreaterThan(0);
      });
      
      result.insights.forEach(insight => {
        expect(insight.length).toBeGreaterThan(0);
      });
      
      result.roleModel.forEach(model => {
        expect(model.length).toBeGreaterThan(0);
      });
    });
  });
});
