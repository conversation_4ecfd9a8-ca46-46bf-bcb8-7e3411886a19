# API Gateway - API Documentation

API Gateway bertindak sebagai single entry point yang meneruskan request ke microservices dan mengekspos endpoint terpadu ke frontend.

## 🔄 Microservice Integration

### 1. Auth Service Integration (Port: 3001)

**Internal API yang Digunakan:**
- `POST /auth/register` - User registration
- `POST /auth/login` - User authentication  
- `GET /auth/profile` - Get user profile
- `PUT /auth/profile` - Update user profile
- `POST /auth/change-password` - Change password
- `POST /auth/logout` - User logout
- `GET /auth/token-balance` - Get user token balance

**Proxy Configuration:**
- Base URL: `AUTH_SERVICE_URL` (default: http://localhost:3001)
- Timeout: 30 seconds
- Headers forwarded: Authorization, X-Forwarded-*, X-Original-URL
- Rate limiting: 5 requests per 15 minutes per IP (auth endpoints)

### 2. Assessment Service Integration (Port: 3003)

**Internal API yang Digunakan:**
- `POST /assessments/submit` - Submit assessment data
- `GET /assessments/status/:jobId` - Check assessment processing status

**Proxy Configuration:**
- Base URL: `ASSESSMENT_SERVICE_URL` (default: http://localhost:3003)
- Timeout: 30 seconds
- Headers forwarded: Authorization, X-User-ID, X-User-Email, X-User-Token-Balance
- Rate limiting: 10 requests per hour per user
- Authentication: Required for all endpoints

### 3. Archive Service Integration (Port: 3002)

**Internal API yang Digunakan:**
- `GET /archive/results` - Get user's analysis results
- `GET /archive/results/:id` - Get specific analysis result
- `PUT /archive/results/:id` - Update analysis result
- `DELETE /archive/results/:id` - Delete analysis result
- `GET /archive/stats` - Get user statistics
- `GET /archive/stats/summary` - Get summary statistics

**Proxy Configuration:**
- Base URL: `ARCHIVE_SERVICE_URL` (default: http://localhost:3002)
- Timeout: 30 seconds
- Headers forwarded: Authorization, X-User-ID, X-User-Email
- Authentication: Required for all endpoints

## 🌐 External API Endpoints

### Authentication Endpoints

#### POST /auth/register
**Description:** Register new user  
**Rate Limit:** 5 requests per 15 minutes per IP  
**Authentication:** Not required  
**Proxied to:** `auth-service:3001/auth/register`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "User registered successfully",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5
    },
    "token": "jwt_token"
  }
}
```

#### POST /auth/login
**Description:** User authentication  
**Rate Limit:** 5 requests per 15 minutes per IP  
**Authentication:** Not required  
**Proxied to:** `auth-service:3001/auth/login`

**Request:**
```json
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Login successful",
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5
    },
    "token": "jwt_token"
  }
}
```

#### GET /auth/profile
**Description:** Get user profile  
**Authentication:** Required (JWT Bearer token)  
**Proxied to:** `auth-service:3001/auth/profile`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response:**
```json
{
  "success": true,
  "data": {
    "user": {
      "id": "uuid",
      "email": "<EMAIL>",
      "token_balance": 5,
      "created_at": "2024-01-01T00:00:00Z"
    }
  }
}
```

#### PUT /auth/profile
**Description:** Update user profile
**Authentication:** Required (JWT Bearer token)
**Proxied to:** `auth-service:3001/auth/profile`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Validation Rules:**
- Email: Valid email format, must be unique

**Response Success (200):**
```json
{
  "success": true,
  "message": "Profile updated successfully",
  "data": {
    "user": {
      "id": "550e8400-e29b-41d4-a716-************",
      "email": "<EMAIL>",
      "token_balance": 3,
      "created_at": "2024-01-01T00:00:00.000Z",
      "updated_at": "2024-01-01T01:00:00.000Z"
    }
  }
}
```

**Response Error (400):**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Email already exists"
  }
}
```

#### POST /auth/change-password
**Description:** Change user password
**Authentication:** Required (JWT Bearer token)
**Proxied to:** `auth-service:3001/auth/change-password`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request Body:**
```json
{
  "currentPassword": "oldPassword123",
  "newPassword": "newSecurePassword456"
}
```

**Validation Rules:**
- currentPassword: Required, must match user's current password
- newPassword: Minimum 8 characters, must contain letters and numbers

**Response Success (200):**
```json
{
  "success": true,
  "message": "Password changed successfully"
}
```

**Response Error (400):**
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Current password is incorrect"
  }
}
```

#### POST /auth/logout
**Description:** User logout (invalidates token on client side)
**Authentication:** Required (JWT Bearer token)
**Proxied to:** `auth-service:3001/auth/logout`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Request Body:** No body required
```json
{}
```

**Response Success (200):**
```json
{
  "success": true,
  "message": "Logout successful"
}
```

#### GET /auth/token-balance
**Description:** Get user token balance
**Authentication:** Required (JWT Bearer token)
**Proxied to:** `auth-service:3001/auth/token-balance`

**Headers:**
```
Authorization: Bearer <jwt_token>
```

**Response Success (200):**
```json
{
  "success": true,
  "data": {
    "user_id": "550e8400-e29b-41d4-a716-************",
    "token_balance": 3
  }
}
```

### Assessment Endpoints

#### POST /assessments/submit
**Description:** Submit assessment data for AI analysis  
**Rate Limit:** 10 requests per hour per user  
**Authentication:** Required  
**Proxied to:** `assessment-service:3003/assessments/submit`

**Headers:**
```
Authorization: Bearer <jwt_token>
Content-Type: application/json
```

**Request:**
```json
{
  "riasec": {
    "realistic": 75,
    "investigative": 85,
    "artistic": 60,
    "social": 50,
    "enterprising": 70,
    "conventional": 55
  },
  "ocean": {
    "openness": 80,
    "conscientiousness": 65,
    "extraversion": 55,
    "agreeableness": 45,
    "neuroticism": 30
  },
  "viaIs": {
    "creativity": 85,
    "curiosity": 78,
    "judgment": 70,
    "loveOfLearning": 82,
    "perspective": 60,
    "bravery": 55,
    "perseverance": 68,
    "honesty": 73,
    "zest": 66,
    "love": 80,
    "kindness": 75,
    "socialIntelligence": 65,
    "teamwork": 60,
    "fairness": 70,
    "leadership": 67,
    "forgiveness": 58,
    "humility": 62,
    "prudence": 69,
    "selfRegulation": 61,
    "appreciationOfBeauty": 50,
    "gratitude": 72,
    "hope": 77,
    "humor": 65,
    "spirituality": 55
  }
}
```

**Response:**
```json
{
  "success": true,
  "message": "Assessment submitted successfully",
  "data": {
    "jobId": "uuid",
    "status": "queued",
    "estimatedProcessingTime": "2-5 minutes"
  }
} 
```

#### GET /assessments/status/:jobId
**Description:** Check assessment processing status  
**Authentication:** Required  
**Proxied to:** `assessment-service:3003/assessments/status/:jobId`

**Response:**
```json
{
  "success": true,
  "data": {
    "jobId": "uuid",
    "status": "processing|completed|failed",
    "progress": 75,
    "estimatedTimeRemaining": "1-2 minutes"
  }
}
```

### Archive Endpoints

#### GET /archive/results
**Description:** Get user's analysis results with pagination  
**Authentication:** Required  
**Proxied to:** `archive-service:3002/archive/results`

**Query Parameters:**
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 10)
- `status` (optional): Filter by status

**Response:**
```json
{
  "success": true,
  "data": {
    "results": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "persona_profile": [...],
        "status": "completed",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 10,
      "total": 25,
      "totalPages": 3
    }
  }
}
```

#### GET /archive/results/:id
**Description:** Get specific analysis result  
**Authentication:** Required  
**Proxied to:** `archive-service:3002/archive/results/:id`

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "assessment_data": {...},
    "persona_profile": [...],
    "status": "completed",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### PUT /archive/results/:id
**Description:** Update analysis result  
**Authentication:** Required  
**Proxied to:** `archive-service:3002/archive/results/:id`
**request body:**
```json
{
  "status": "completed",
  "persona_profile": [
    {
      "archetype": "The Innovator",
      "personality_summary": "Creative and forward-thinking individual..."
    }
  ]
}
```
**Response:**
```json
{
  "success": true,
  "message": "Analysis result updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "updated_at": "2025-07-16T07:46:44.000Z"
  }
}
```

#### DELETE /archive/results/:id
**Description:** Delete analysis result  
**Authentication:** Required  
**Proxied to:** `archive-service:3002/archive/results/:id`
**Response:**
```json
{
  "success": true,
  "message": "Analysis result updated successfully",
  "data": {
    "id": "550e8400-e29b-41d4-a716-************",
    "updated_at": "2025-07-16T07:46:44.000Z"
  }
}
```

#### GET /archive/stats
**Description:** Get user statistics
**Authentication:** Required
**Proxied to:** `archive-service:3002/archive/stats`
**Response:**
```json
{
  "success": true,
  "data": {
    "total_analyses": 15,
    "completed": 12,
    "processing": 2,
    "failed": 1,
    "latest_analysis": "2025-07-16T07:30:00.000Z",
    "most_common_archetype": "The Innovator"
  }
}
```

#### GET /archive/stats/overview
**Description:** Get user overview statistics
**Authentication:** Required
**Proxied to:** `archive-service:3002/archive/stats/overview`
**Response:**
```json
{
  "success": true,
  "data": {
    "user_stats": {
      "total_analyses": 5,
      "completed_analyses": 4,
      "processing_analyses": 1,
      "last_analysis_date": "2025-07-16T07:30:00.000Z"
    },
    "recent_archetypes": [
      {
        "archetype": "The Innovator",
        "date": "2025-07-16T07:30:00.000Z"
      },
      {
        "archetype": "The Analyst", 
        "date": "2025-07-15T14:20:00.000Z"
      }
    ]
  }
}
```


### Health Check Endpoints

#### GET /health
**Description:** Comprehensive health check of all services  
**Authentication:** Not required  
**Rate Limit:** Excluded from rate limiting

**Response:**
```json
{
  "status": "healthy|degraded|error",
  "timestamp": "2024-01-01T00:00:00Z",
  "responseTime": "45ms",
  "version": "1.0.0",
  "services": {
    "auth-service": {
      "status": "healthy",
      "responseTime": "12ms"
    },
    "assessment-service": {
      "status": "healthy",
      "responseTime": "8ms"
    },
    "archive-service": {
      "status": "healthy",
      "responseTime": "15ms"
    }
  },
  "gateway": {
    "status": "healthy",
    "uptime": 3600,
    "memory": {...}
  }
}
```

#### GET /health/live
**Description:** Simple liveness probe  
**Authentication:** Not required

#### GET /health/ready
**Description:** Readiness probe  
**Authentication:** Not required

#### GET /health/detailed
**Description:** Extended health information  
**Authentication:** Not required

### Gateway Information

#### GET /
**Description:** API Gateway information  
**Authentication:** Not required

**Response:**
```json
{
  "success": true,
  "message": "ATMA API Gateway is running",
  "version": "1.0.0",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

## 🔒 Security Features

- **JWT Authentication:** All protected endpoints require valid JWT token
- **Rate Limiting:** Different limits for different endpoint categories
- **CORS Protection:** Configurable cross-origin resource sharing
- **Security Headers:** Helmet.js security headers
- **Request Logging:** Comprehensive request/response logging
- **Error Handling:** Consistent error response format

## 📊 Rate Limiting

- **General:** 1000 requests per 10 minutes per IP
- **Auth Endpoints:** 1000 requests per 10 minutes per IP
- **Assessment Endpoints:** 1000 requests per 10 minutes per user
- **Health Endpoints:** Excluded from rate limiting

## 🔄 Request Flow

1. **Frontend** → **API Gateway** (Port 3000)
2. **API Gateway** → **Microservice** (auth:3001, assessment:3003, archive:3002)
3. **Microservice** → **API Gateway** → **Frontend**

All requests are logged, rate-limited, and authenticated as appropriate before being proxied to the target microservice.
