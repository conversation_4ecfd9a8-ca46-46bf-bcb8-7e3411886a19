# Server Configuration
PORT=3000
NODE_ENV=development

# JWT Configuration
JWT_SECRET=generate_a_strong_random_secret_key_at_least_32_characters_long

# Service URLs
AUTH_SERVICE_URL=http://localhost:3001
ARCHIVE_SERVICE_URL=http://localhost:3002
ASSESSMENT_SERVICE_URL=http://localhost:3003
NOTIFICATION_SERVICE_URL=http://localhost:3005

# Rate Limiting Configuration - 1000 requests per 10 minutes
RATE_LIMIT_WINDOW_MS=600000
RATE_LIMIT_MAX_REQUESTS=1000

# CORS Configuration - Unlimited access (origin: true)
CORS_ORIGIN=*

# Logging Configuration
LOG_LEVEL=info
LOG_FILE=logs/api-gateway.log
